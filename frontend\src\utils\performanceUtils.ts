// 性能监控和分析工具

// 内存使用监控
export const getMemoryUsage = (): {
  used: number;
  total: number;
  percentage: number;
} => {
  const memory = (performance as any).memory;
  
  if (!memory) {
    return { used: 0, total: 0, percentage: 0 };
  }
  
  return {
    used: memory.usedJSHeapSize,
    total: memory.totalJSHeapSize,
    percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
  };
};

// 渲染性能监控
export const measureRenderTime = <T extends any[]>(
  fn: (...args: T) => void,
  name: string = 'render'
) => {
  return (...args: T) => {
    const startTime = performance.now();
    const result = fn(...args);
    const endTime = performance.now();
    
    console.log(`${name} took ${(endTime - startTime).toFixed(2)}ms`);
    
    return result;
  };
};

// 防抖函数性能优化版本
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  let lastCallTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    const callNow = immediate && !timeout;
    
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate) {
        func(...args);
      }
    }, wait);
    
    if (callNow) {
      func(...args);
    }
    
    lastCallTime = now;
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

// 长任务检测
export const detectLongTasks = (threshold: number = 50): void => {
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > threshold) {
          console.warn(`Long task detected: ${entry.duration.toFixed(2)}ms`, entry);
        }
      });
    });
    
    observer.observe({ entryTypes: ['longtask'] });
  }
};

// FPS监控
export class FPSMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 0;
  private isRunning = false;
  private animationId: number | null = null;

  start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.measure();
  }

  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  getFPS(): number {
    return this.fps;
  }

  private measure = (): void => {
    if (!this.isRunning) return;
    
    this.frameCount++;
    const currentTime = performance.now();
    
    if (currentTime >= this.lastTime + 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
    }
    
    this.animationId = requestAnimationFrame(this.measure);
  };
}

// 组件渲染次数监控
export const createRenderCounter = (componentName: string) => {
  let renderCount = 0;
  
  return {
    increment: () => {
      renderCount++;
      console.log(`${componentName} rendered ${renderCount} times`);
    },
    getCount: () => renderCount,
    reset: () => {
      renderCount = 0;
    },
  };
};

// 内存泄漏检测
export const detectMemoryLeaks = (
  testFunction: () => void,
  iterations: number = 10,
  threshold: number = 5 * 1024 * 1024 // 5MB
): Promise<boolean> => {
  return new Promise((resolve) => {
    const initialMemory = getMemoryUsage().used;
    
    const runTest = (iteration: number) => {
      if (iteration >= iterations) {
        // 强制垃圾回收（如果可用）
        if ((global as any).gc) {
          (global as any).gc();
        }
        
        setTimeout(() => {
          const finalMemory = getMemoryUsage().used;
          const memoryIncrease = finalMemory - initialMemory;
          
          console.log(`Memory increase after ${iterations} iterations: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
          
          resolve(memoryIncrease > threshold);
        }, 100);
        
        return;
      }
      
      testFunction();
      
      // 使用setTimeout来避免阻塞主线程
      setTimeout(() => runTest(iteration + 1), 10);
    };
    
    runTest(0);
  });
};

// 性能报告生成器
export const generatePerformanceReport = (): {
  memory: ReturnType<typeof getMemoryUsage>;
  timing: PerformanceTiming;
  navigation: PerformanceNavigation;
} => {
  return {
    memory: getMemoryUsage(),
    timing: performance.timing,
    navigation: performance.navigation,
  };
};

// 性能日志管理器
class PerformanceLogger {
  private static instance: PerformanceLogger;
  private logCount = 0;
  private lastLogTime = 0;
  private lastMemoryCheck = 0;
  private maxLogsPerSecond = 5; // 减少到每秒最多5条日志
  private maxTotalLogs = 500; // 减少到总共最多500条日志
  private isEnabled = process.env.NODE_ENV === 'development';
  private memoryThreshold = 150 * 1024 * 1024; // 提高到150MB内存阈值
  private memoryCheckInterval = 10000; // 内存检查间隔10秒

  static getInstance(): PerformanceLogger {
    if (!PerformanceLogger.instance) {
      PerformanceLogger.instance = new PerformanceLogger();
    }
    return PerformanceLogger.instance;
  }

  private checkMemoryUsage(): boolean {
    const now = Date.now();
    // 减少内存检查频率
    if (now - this.lastMemoryCheck < this.memoryCheckInterval) {
      return true; // 如果还没到检查时间，假设内存正常
    }

    this.lastMemoryCheck = now;
    const memory = getMemoryUsage();
    return memory.used < this.memoryThreshold;
  }

  private shouldLog(): boolean {
    if (!this.isEnabled) return false;

    // 检查内存使用情况（减少检查频率）
    if (!this.checkMemoryUsage()) {
      if (this.logCount % 200 === 0) { // 减少警告频率：每200次检查时警告一次
        console.warn('⚠️ 性能日志已禁用：内存使用过高');
      }
      return false;
    }

    // 检查总日志数量限制
    if (this.logCount >= this.maxTotalLogs) {
      if (this.logCount === this.maxTotalLogs) {
        console.warn('⚠️ 性能日志已达到最大数量限制，停止记录');
      }
      return false;
    }

    // 检查频率限制
    const now = Date.now();
    if (now - this.lastLogTime < 1000 / this.maxLogsPerSecond) {
      return false;
    }

    this.lastLogTime = now;
    this.logCount++;
    return true;
  }

  log(
    id: string,
    phase: 'mount' | 'update',
    actualDuration: number,
    _baseDuration: number,
    _startTime: number,
    _commitTime: number
  ): void {
    if (!this.shouldLog()) return;

    // 提高记录阈值，只记录更有意义的性能数据
    if (actualDuration > 5 || phase === 'mount') { // 从1ms提高到5ms
      // 使用更简洁的日志格式，减少内存占用
      console.log(`Profiler [${id}] ${phase}: ${actualDuration.toFixed(1)}ms`);
    }
  }

  // 重置日志计数器
  reset(): void {
    this.logCount = 0;
    this.lastLogTime = 0;
  }

  // 禁用日志
  disable(): void {
    this.isEnabled = false;
  }

  // 启用日志
  enable(): void {
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  // 获取状态
  getStatus(): { enabled: boolean; logCount: number; memoryOk: boolean } {
    return {
      enabled: this.isEnabled,
      logCount: this.logCount,
      memoryOk: this.checkMemoryUsage()
    };
  }
}

// React DevTools Profiler 辅助函数 - 重构为使用PerformanceLogger
export const logProfilerData = (
  id: string,
  phase: 'mount' | 'update',
  actualDuration: number,
  baseDuration: number,
  startTime: number,
  commitTime: number
): void => {
  PerformanceLogger.getInstance().log(
    id,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime
  );
};

// 导出性能日志管理器的控制方法
export const performanceLogger = {
  reset: () => PerformanceLogger.getInstance().reset(),
  disable: () => PerformanceLogger.getInstance().disable(),
  enable: () => PerformanceLogger.getInstance().enable(),
  getStatus: () => PerformanceLogger.getInstance().getStatus(),
};

// 紧急禁用性能分析器的全局函数
export const emergencyDisableProfiler = (): void => {
  performanceLogger.disable();
  console.warn('🚨 性能分析器已紧急禁用！如需重新启用，请刷新页面并在URL中添加 ?disable-profiler=false');

  // 在全局对象上添加控制函数，方便在控制台中使用
  if (typeof window !== 'undefined') {
    // 清理之前可能存在的函数，避免内存泄漏
    delete (window as any).enableProfiler;
    delete (window as any).disableProfiler;
    delete (window as any).profilerStatus;

    (window as any).enableProfiler = () => {
      performanceLogger.enable();
      console.log('✅ 性能分析器已重新启用');
    };

    (window as any).disableProfiler = () => {
      performanceLogger.disable();
      console.log('❌ 性能分析器已禁用');
    };

    (window as any).profilerStatus = () => {
      const status = performanceLogger.getStatus();
      console.log('📊 性能分析器状态:', status);
      return status;
    };
  }
};

// 内存监控服务
class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;
  private checkCount = 0;
  private maxChecks = 200; // 增加到200次检查
  private lastWarningTime = 0;
  private warningCooldown = 60000; // 警告冷却时间60秒

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  start(): void {
    if (typeof window === 'undefined' || this.intervalId) return;

    const checkMemoryPressure = () => {
      this.checkCount++;

      if (this.checkCount > this.maxChecks) {
        this.stop();
        return;
      }

      const memory = getMemoryUsage();
      const now = Date.now();

      // 调整阈值：95%紧急禁用，85%警告
      if (memory.percentage > 95) {
        console.error(`🚨 内存使用过高 (${memory.percentage.toFixed(1)}%)，立即禁用性能分析器！`);
        emergencyDisableProfiler();
        this.stop();
      }
      // 85%警告，但有冷却时间
      else if (memory.percentage > 85 && now - this.lastWarningTime > this.warningCooldown) {
        console.warn(`⚠️ 内存使用较高 (${memory.percentage.toFixed(1)}%)，建议关闭不必要的标签页`);
        this.lastWarningTime = now;
      }
    };

    // 每60秒检查一次内存使用情况（从30秒增加到60秒）
    this.intervalId = setInterval(checkMemoryPressure, 60000);

    // 页面卸载时清理
    window.addEventListener('beforeunload', this.cleanup);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private cleanup = (): void => {
    this.stop();
    window.removeEventListener('beforeunload', this.cleanup);
  };

  reset(): void {
    this.checkCount = 0;
    this.lastWarningTime = 0;
  }
}

// 自动检测内存压力并采取行动
export const setupMemoryPressureDetection = (): void => {
  MemoryMonitor.getInstance().start();
};
